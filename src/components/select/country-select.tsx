"use client";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Check, ChevronDown } from "lucide-react";
import { useId, useState } from "react";
import { countryList } from "@/lib/country";

interface CountrySelectProps {
  value?: string;
  onValueChange?: (value: string, code: string) => void;
  placeholder?: string;
  label?: string;
  className?: string;
  disabled?: boolean;
}

export function CountrySelect({
  value,
  onValueChange,
  placeholder = "选择国家",
  label,
  className,
  disabled = false,
}: CountrySelectProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);

  // 根据传入的 value（英文国家名）查找对应的国家
  const selectedCountry = countryList.find((country) => {
    return country.englishName === value;
  });

  return (
    <div className={cn("space-y-2", className)}>
      {label && <Label htmlFor={id}>{label}</Label>}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            disabled={disabled}
            className="w-full justify-between bg-background px-3 font-normal outline-offset-0 hover:bg-background focus-visible:border-ring focus-visible:outline-[3px] focus-visible:outline-ring/20"
          >
            <span className={cn("truncate", !value && "text-muted-foreground")}>
              {selectedCountry ? selectedCountry.label : placeholder}
            </span>
            <ChevronDown
              size={16}
              strokeWidth={2}
              className="shrink-0 text-muted-foreground/80"
              aria-hidden="true"
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full min-w-[var(--radix-popper-anchor-width)] border-input p-0"
          align="start"
        >
          <Command>
            <CommandInput placeholder="搜索国家..." />
            <CommandList>
              <CommandEmpty>未找到国家。</CommandEmpty>
              <CommandGroup>
                {countryList.map((country) => (
                  <CommandItem
                    key={country.value}
                    value={country.value}
                    onSelect={(currentValue) => {
                      const selectedCountry = countryList.find(c => c.value === currentValue);
                      if (selectedCountry) {
                        const englishName = selectedCountry.englishName;
                        const countryCode = selectedCountry.value;

                        // 如果当前选中的就是这个国家，则清空选择
                        if (value === englishName) {
                          onValueChange?.("", "");
                        } else {
                          onValueChange?.(englishName, countryCode);
                        }
                      }
                      setOpen(false);
                    }}
                  >
                    {country.label}
                    {value === country.englishName && (
                      <Check size={16} strokeWidth={2} className="ml-auto" />
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
