import useSearchParamsManager from "@/hooks/use-url-param";
import { DataTable } from "@/components/table/base-table";
import { DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight } from "@/components/table/toolbar/data-table-toolbar";
import { changeWaybillChannel, countWaybillSupplierOrderScanned, pageWaybill, retryWaybillBatch } from "@/api/order/waybill/waybill-api";
import { batchRefreshTrackingByWaybillIds } from "@/api/tracking/tracking-api";
import { WaybillColumns } from "./table/waybill-column";
import { SelectWithInput } from "@/components/ui/select-with-input";
import { DataTableUrlFilter } from "@/components/select/data-table-column-filter";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import dayjs from "dayjs";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { ExpandableTabs } from "@/components/ui/expandable-tabs";
import { Ban, DollarSign, File, RefreshCw, RotateCcw, Send, Star, Truck, X } from "lucide-react";
import { pushModal } from "@/components/modals";
import { toast } from "sonner";
import { refreshTableAtom } from "@/state/common";
import { useSetAtom } from "jotai";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { CountrySelect } from "@/components/select/country-select";

export default function WaybillPage() {

    const { addParam, getParam } = useSearchParamsManager();

    const refresh = useSetAtom(refreshTableAtom);

    const handleChangeChannel = (subOrderIds: string[]) => {
        pushModal('ChannelSelectModels', {
            onConfirm: (company: string, route: string) => {
                changeWaybillChannel(subOrderIds, company, route)
                    .then(() => {
                        toast.success('修改成功');
                        refresh(prev => prev + 1);
                    })
                    .catch((error) => {
                        toast.error('修改失败:' + error.message);
                    })
            }
        })
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">运单管理</h1>
            </div>
            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={WaybillColumns}
                    onFetch={param => pageWaybill(param)}
                    isFixedHeader={false}
                    containerHeight="620px"
                    isNeedSelect={true}
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <div className="flex flex-col gap-2 ">
                                    <div className="flex items-center gap-2">
                                        <ToolbarItem>
                                            <SelectWithInput
                                                options={[
                                                    { label: "订单号", value: "orderNo" },
                                                    { label: "SPU", value: "spu" },
                                                    { label: "上传文件名", value: "fileName" },
                                                    { label: "主任务编号", value: "mainOrderId" },
                                                    { label: "运单号", value: "waybillNo" },
                                                ]}
                                                inputPlaceholder="请输入搜索内容"
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="status"
                                                title="任务状态"
                                                options={
                                                    [
                                                        { label: '已创建', value: 'CREATED' },
                                                        { label: '待处理', value: 'PENDING' },
                                                        { label: '已完成', value: 'COMPLETED' },
                                                        { label: '失败', value: 'FAILED' },
                                                        { label: '已取消', value: 'CANCELLED' },
                                                    ]
                                                }
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <CountrySelect
                                                value={getParam('country') || ''}
                                                onValueChange={(value) => addParam('country', value)}
                                                placeholder="选择国家"
                                                className="w-48"
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DateRangePicker
                                                onUpdate={(values) => {
                                                    addParam('createdAtFrom', dayjs(values.range.from).format('YYYY-MM-DD'))
                                                    addParam('createdAtTo', dayjs(values.range.to).format('YYYY-MM-DD'))
                                                }}
                                                initialDateFrom={getParam('createdAtFrom') ? dayjs(getParam('createdAtFrom')).toDate() : dayjs().subtract(365, 'day').toDate()}
                                                initialDateTo={getParam('createdAtTo') ? dayjs(getParam('createdAtTo')).toDate() : dayjs().toDate()}
                                                align="start"
                                                locale="zh-CN"
                                                showCompare={false}
                                            />
                                        </ToolbarItem>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <ToolbarItem>
                                            <Textarea
                                                placeholder="搜索订单,多个订单用空行隔开..."
                                                className="text-xs w-96 min-h-[60px]"
                                                defaultValue={getParam('orderNos') || ''}
                                                onChange={(e) => addParam('orderNos', e.target.value)}
                                            />
                                        </ToolbarItem>

                                        <ToolbarItem>
                                            <Textarea
                                                placeholder="搜索运单,多个运单用空行隔开..."
                                                className="text-xs w-96 min-h-[60px]"
                                                defaultValue={getParam('waybillNos') || ''}
                                                onChange={(e) => addParam('waybillNos', e.target.value)}
                                            />
                                        </ToolbarItem>
                                    </div>
                                </div>


                                {table.getSelectedRowModel().rows.length > 0 && (
                                    // <Dock>
                                    //     <DockItem icon={Truck} label="选择渠道" onClick={() => handleChangeChannel(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                    //     <DockItem icon={Merge} label="合并" onClick={() => handleMerge(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                    //     <DockItem icon={Trash2} label="删除" onClick={() => handleDelete(table.getSelectedRowModel().rows.map((row: any) => row.original.id))} />
                                    // </Dock>
                                    <ExpandableTabs tabs={[{
                                        title: '提交运单',
                                        icon: Send,
                                        onClick: () => {
                                            const selectedRows = table.getSelectedRowModel().rows;
                                            if (selectedRows.length !== 1) {
                                                toast.error('请选择一个运单进行提交');
                                                return;
                                            }
                                            pushModal('SubmitWaybillModal', { 
                                                waybillId: selectedRows[0].original.id,
                                                onSuccess: () => {
                                                    refresh(prev => prev + 1);
                                                    table.setRowSelection({});
                                                }
                                            })
                                        }
                                    },
                                    {
                                        title: '设置快速通道税费',
                                        icon: DollarSign,
                                        onClick: () => pushModal('SetWaybillTaxModal', { waybillIds: table.getSelectedRowModel().rows.map(row => row.original.id) })
                                    },
                                    {
                                        title: '选择渠道',
                                        icon: Truck,
                                        onClick: () => handleChangeChannel(table.getSelectedRowModel().rows.map(row => row.original.id))
                                    },
                                    {
                                        title: '重试',
                                        icon: RotateCcw,
                                        onClick: async () => {
                                            pushModal('DoubleCheckModal', {
                                                title: '重试',
                                                description: '确定重试吗？',
                                                onCancel: () => {
                                                    table.setRowSelection({});
                                                },
                                                onConfirm: async () => {
                                                    const rows = table.getSelectedRowModel().rows.map(row => row.original)
                                                    const ids = rows.map(row => row.id)
                                                    const r = await countWaybillSupplierOrderScanned(ids)
                                                    const reminder: string[] = []
                                                    rows.forEach(row => {
                                                        const scannedResult = r[row.id]
                                                        if (scannedResult && scannedResult.total != 0 && scannedResult.scanned == scannedResult.total) {
                                                            reminder.push(row.orderNos)
                                                        }
                                                    })

                                                    const doRetry = () => {
                                                        toast.promise(
                                                            retryWaybillBatch(ids),
                                                            {
                                                                loading: '重试中...',
                                                                success: () => {
                                                                    refresh(prev => prev + 1);
                                                                    table.setRowSelection({});
                                                                    return '重试成功';
                                                                },
                                                                error: (error) => {
                                                                    return '重试失败:' + error.message;
                                                                }
                                                            }
                                                        )
                                                    }

                                                    if (reminder.length) {
                                                        const element = <div className="flex flex-col">
                                                            {reminder.map(orderNos =>
                                                                <>
                                                                    {orderNos.split(',').map(orderNo => <span>{orderNo}</span>)}
                                                                    <Separator className="my-0.5" />
                                                                </>
                                                            )}
                                                        </div>

                                                        pushModal('DoubleCheckModal', {
                                                            title: '存在已经出库的运单',
                                                            description: "以下运单已出库,请确认继续重试",
                                                            children: element,
                                                            onConfirm: () => {
                                                                doRetry()
                                                            },
                                                            onCancel: () => {
                                                            }
                                                        })
                                                    } else {
                                                        doRetry()
                                                    }
                                                }
                                            })


                                        }
                                    },
                                    {
                                        title: '导出excel',
                                        icon: File,
                                        onClick: () => pushModal('WaybillExcelExportModels', { waybillIds: table.getSelectedRowModel().rows.map(row => row.original.id) })
                                    },
                                    {
                                        title: '停止打印设置',
                                        icon: X,
                                        onClick: () => pushModal('StopWaybillPrintModal', { waybillIds: table.getSelectedRowModel().rows.map(row => row.original.id) })
                                    },
                                    {
                                        title: '取消停止打印设置',
                                        icon: Ban,
                                        onClick: () => pushModal('CancelStopWaybillPrintModal', { waybillIds: table.getSelectedRowModel().rows.map(row => row.original.id) })
                                    },
                                    {
                                        title: '批量刷新轨迹',
                                        icon: Star,
                                        onClick: () => {
                                            const waybillIds = table.getSelectedRowModel().rows.map(row => row.original.id);
                                            toast.promise(
                                                batchRefreshTrackingByWaybillIds(waybillIds),
                                                {
                                                    loading: '批量刷新轨迹中...',
                                                    success: (result) => {
                                                        refresh(prev => prev + 1);
                                                        table.setRowSelection({});
                                                        return `批量刷新完成: 成功${result.successCount}个, 失败${result.failureCount}个, 跳过${result.skippedCount}个`;
                                                    },
                                                    error: (error) => {
                                                        return '批量刷新失败: ' + error.message;
                                                    }
                                                }
                                            );
                                        }
                                    }
                                    ]}
                                    />
                                )}
                            </ToolbarLeft>

                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    )
}