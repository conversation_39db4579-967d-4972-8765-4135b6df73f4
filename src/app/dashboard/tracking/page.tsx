import { pageTracking, triggerTrackingUpdate, cleanupStaleTracking, getSupportedChannels } from "@/api/tracking/tracking-api";
import useSearchParamsManager from "@/hooks/use-url-param";
import { pushModal } from "@/components/modals";
import { DataTableUrlFilter } from "@/components/select/data-table-column-filter";
import { DataTable } from "@/components/table/base-table";
import { DataTableToolbar, ToolbarItem, ToolbarLeft, ToolbarRight } from "@/components/table/toolbar/data-table-toolbar";
import { DataTableViewOptions } from "@/components/table/toolbar/data-table-view-options";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { SelectWithInput } from "@/components/ui/select-with-input";
import { Button } from "@/components/ui/button";
import { refreshTableAtom } from "@/state/common";
import dayjs from "dayjs";
import { useSet<PERSON><PERSON> } from "jotai";
import { RefreshCw, Trash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { toast } from "sonner";
import { TrackingColumns } from "./table/tracking-column";
import { TrackingStatus, WaybillChannel } from "@/api/tracking/tracking-model";
import { BatchSearchInput } from "@/components/ui/batch-search-input";
import { CountrySelect } from "@/components/select/country-select";

export default function TrackingPage() {
    const { addParam, getParam } = useSearchParamsManager();
    const refresh = useSetAtom(refreshTableAtom);

    // 从 URL 参数读取日期范围，如果没有则使用默认值
    const createdAtFrom = getParam('createdAtFrom');
    const createdAtTo = getParam('createdAtTo');

    const initialDateFrom = createdAtFrom
        ? dayjs(createdAtFrom).toDate()
        : dayjs().subtract(7, 'day').toDate();

    const initialDateTo = createdAtTo
        ? dayjs(createdAtTo).toDate()
        : dayjs().toDate();

    const handleTriggerUpdate = async () => {
        try {
            await triggerTrackingUpdate();
            toast.success('轨迹更新任务已启动');
        } catch (error: any) {
            toast.error('启动更新任务失败: ' + error.message);
        }
    };

    const handleCleanup = () => {
        pushModal('DoubleCheckModal', {
            title: '清理过期轨迹',
            description: '确定要清理过期的轨迹信息吗？这将删除15天内无更新的轨迹记录。',
            onConfirm: async () => {
                try {
                    const result = await cleanupStaleTracking();
                    toast.success(`清理完成，删除了 ${result.deletedCount} 条记录`);
                    refresh(prev => prev + 1);
                } catch (error: any) {
                    toast.error('清理失败: ' + error.message);
                }
            },
            onCancel: () => {
                console.log('取消清理');
            }
        });
    };

    const handleViewStatistics = () => {
        pushModal('TrackingStatisticsModal', {
            onClose: () => {
                refresh(prev => prev + 1);
            }
        });
    };

    // 状态选项
    const statusOptions = Object.values(TrackingStatus).map(status => ({
        label: status.display,
        value: status.code
    }));

    // 渠道选项
    const channelOptions = Object.values(WaybillChannel).map(channel => ({
        label: channel.display,
        value: channel.code
    }));

    return (
        <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">轨迹管理</h1>
                <div className="flex items-center gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleViewStatistics}
                    >
                        <BarChart3 className="h-4 w-4 mr-2" />
                        统计信息
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleTriggerUpdate}
                    >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        更新轨迹
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCleanup}
                    >
                        <Trash2 className="h-4 w-4 mr-2" />
                        清理过期
                    </Button>
                </div>
            </div>

            <div className="rounded-lg border shadow-sm p-4">
                <DataTable
                    columns={TrackingColumns}
                    onFetch={param => pageTracking(param)}
                    isFixedHeader={false}
                    isNeedSelect={false}
                    containerHeight="620px"
                    toolbar={(table, tableId) => (
                        <DataTableToolbar table={table} tableId={tableId}>
                            <ToolbarLeft>
                                <div className="flex flex-col gap-2">
                                    <div className="flex items-center gap-2">
                                        <ToolbarItem>
                                            <SelectWithInput
                                                options={[
                                                    { label: "运单号", value: "waybillNo" },
                                                    { label: "跟踪号", value: "trackingNumber" },
                                                    { label: "订单号", value: "orderNo" }
                                                ]}
                                                inputPlaceholder="请输入搜索内容"
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="channel"
                                                title="渠道"
                                                options={channelOptions}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="status"
                                                title="状态"
                                                options={statusOptions}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DataTableUrlFilter
                                                paramKey="completed"
                                                title="是否完成"
                                                options={[
                                                    { label: '已完成', value: 'true' },
                                                    { label: '未完成', value: 'false' },
                                                ]}
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <CountrySelect
                                                value={getParam('destinationCountry') || ''}
                                                onValueChange={(value) => addParam('destinationCountry', value)}
                                                placeholder="目的地国家"
                                                className="w-48"
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <CountrySelect
                                                value={getParam('originCountry') || ''}
                                                onValueChange={(value) => addParam('originCountry', value)}
                                                placeholder="起始国家"
                                                className="w-48"
                                            />
                                        </ToolbarItem>
                                        <ToolbarItem>
                                            <DateRangePicker
                                                onUpdate={(values) => {
                                                    addParam('createdAtFrom', dayjs(values.range.from).format('YYYY-MM-DD'))
                                                    addParam('createdAtTo', dayjs(values.range.to).format('YYYY-MM-DD'))
                                                }}
                                                initialDateFrom={initialDateFrom}
                                                initialDateTo={initialDateTo}
                                                align="start"
                                                locale="zh-CN"
                                                showCompare={false}
                                            />
                                        </ToolbarItem>
                                    </div>
                                    <div className="flex items-center space-x-4">
                                        <ToolbarItem>
                                            <BatchSearchInput
                                                options={[
                                                    {
                                                        label: "订单号",
                                                        value: "orderNos",
                                                        placeholder: "搜索订单号，多个订单号用换行分隔..."
                                                    },
                                                    {
                                                        label: "运单号",
                                                        value: "waybillNos",
                                                        placeholder: "搜索运单号，多个运单号用换行分隔..."
                                                    },
                                                    {
                                                        label: "跟踪号",
                                                        value: "trackingNumbers",
                                                        placeholder: "搜索跟踪号，多个跟踪号用换行分隔..."
                                                    }
                                                ]}
                                                className="w-96"
                                                defaultValue={table.getColumn('orderNos')?.getFilterValue() as string | undefined ||
                                                    table.getColumn('waybillNos')?.getFilterValue() as string | undefined ||
                                                    table.getColumn('trackingNumbers')?.getFilterValue() as string | undefined || ''}
                                                onValueChange={(type, value) => {
                                                    // 清除其他批量搜索的过滤值
                                                    table.getColumn('orderNos')?.setFilterValue(type === 'orderNos' ? value : undefined);
                                                    table.getColumn('waybillNos')?.setFilterValue(type === 'waybillNos' ? value : undefined);
                                                    table.getColumn('trackingNumbers')?.setFilterValue(type === 'trackingNumbers' ? value : undefined);
                                                }}
                                            />
                                        </ToolbarItem>
                                    </div>
                                </div>
                            </ToolbarLeft>
                            <ToolbarRight>
                                <ToolbarItem>
                                    <DataTableViewOptions table={table} tableId={tableId} />
                                </ToolbarItem>
                            </ToolbarRight>
                        </DataTableToolbar>
                    )}
                    dependencies={[]}
                />
            </div>
        </div>
    );
}
