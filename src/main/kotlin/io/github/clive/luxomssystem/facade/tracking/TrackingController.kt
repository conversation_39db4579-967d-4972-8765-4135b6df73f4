package io.github.clive.luxomssystem.facade.tracking

import io.github.clive.luxomssystem.application.tracking.TrackingManagementService
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.tracking.model.TrackingInfo
import io.github.clive.luxomssystem.facade.tracking.request.TrackingPageRequest
import io.github.clive.luxomssystem.infrastructure.tracking.repository.TrackingInfoRepository
import io.github.clive.luxomssystem.infrastructure.tracking.scheduler.TrackingUpdateScheduler
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 轨迹管理Controller
 * 提供轨迹查询、更新、统计等API接口
 */
@RestController
@RequestMapping("/api/tracking")
class TrackingController(
    private val trackingInfoRepository: TrackingInfoRepository,
    private val trackingManagementService: TrackingManagementService,
    private val trackingUpdateScheduler: TrackingUpdateScheduler
) {
    
    private val log = KotlinLogging.logger {}

    /**
     * 分页查询轨迹信息
     */
    @PostMapping("/page")
    fun pageTracking(
        @RequestBody req: TrackingPageRequest,
        paramReq: TrackingPageRequest,
    ) = trackingManagementService.pageTracking(
        req.apply {
            // 如果 paramReq 中有 URL 参数，则使用 paramReq 的值覆盖 req 的值
            if (paramReq.waybillNo != null) waybillNo = paramReq.waybillNo
            if (paramReq.trackingNumber != null) trackingNumber = paramReq.trackingNumber
            if (paramReq.orderNo != null) orderNo = paramReq.orderNo
            if (paramReq.channel != null) channel = paramReq.channel
            if (paramReq.destinationCountry != null) destinationCountry = paramReq.destinationCountry
            if (paramReq.destinationCountryCode != null) destinationCountryCode = paramReq.destinationCountryCode
            if (paramReq.originCountry != null) originCountry = paramReq.originCountry
            if (paramReq.originCountryCode != null) originCountryCode = paramReq.originCountryCode
            if (paramReq.waybillId != null) waybillId = paramReq.waybillId
            if (paramReq.createdAtFrom != null) createdAtFrom = paramReq.createdAtFrom
            if (paramReq.createdAtTo != null) createdAtTo = paramReq.createdAtTo
            if (paramReq.status.isNotEmpty()) status = paramReq.status
            if (paramReq.page != null) page = paramReq.page
            if (paramReq.size != null) size = paramReq.size
            if (paramReq.sort != null) sort = paramReq.sort
        }
    ).toResponse()

    /**
     * 根据运单号查询轨迹信息
     */
    @GetMapping("/waybill/{waybillNo}")
    fun getTrackingByWaybillNo(@PathVariable waybillNo: String): ResponseEntity<TrackingInfoResponse> {
        val trackingInfo = trackingInfoRepository.findByWaybillNo(waybillNo)
        return if (trackingInfo != null) {
            ResponseEntity.ok(TrackingInfoResponse.fromDomain(trackingInfo))
        } else {
            ResponseEntity.notFound().build()
        }
    }

    /**
     * 根据订单号查询轨迹信息
     */
    @GetMapping("/order/{orderNo}")
    fun getTrackingByOrderNo(@PathVariable orderNo: String): List<TrackingInfoResponse> {
        val trackingInfoList = trackingInfoRepository.findByOrderNos(orderNo)
        return trackingInfoList.map { TrackingInfoResponse.fromDomain(it) }
    }

    /**
     * 根据运单ID查询轨迹信息
     */
    @GetMapping("/waybill-id/{waybillId}")
    fun getTrackingByWaybillId(@PathVariable waybillId: Long): ResponseEntity<TrackingInfoResponse> {
        val trackingInfo = trackingInfoRepository.findByWaybillId(waybillId)
        return if (trackingInfo != null) {
            ResponseEntity.ok(TrackingInfoResponse.fromDomain(trackingInfo))
        } else {
            ResponseEntity.notFound().build()
        }
    }

    /**
     * 手动更新指定运单的轨迹信息
     */
    @PostMapping("/update/waybill/{waybillNo}")
    fun updateTrackingByWaybillNo(@PathVariable waybillNo: String): ResponseEntity<TrackingUpdateResponse> {
        log.info { "手动触发轨迹更新 | 运单号: $waybillNo" }
        
        val result = trackingManagementService.updateTrackingByWaybillNo(waybillNo)
        return ResponseEntity.ok(TrackingUpdateResponse.fromResult(result))
    }

    /**
     * 强制更新指定运单的轨迹信息（忽略15天限制）
     */
    @PostMapping("/force-update/waybill/{waybillNo}")
    fun forceUpdateTracking(@PathVariable waybillNo: String): ResponseEntity<TrackingUpdateResponse> {
        log.info { "强制更新轨迹 | 运单号: $waybillNo" }
        
        val result = trackingManagementService.forceUpdateTracking(waybillNo)
        return ResponseEntity.ok(TrackingUpdateResponse.fromResult(result))
    }

    /**
     * 手动更新指定渠道的所有轨迹信息
     */
    @PostMapping("/update/channel/{channel}")
    fun updateTrackingByChannel(@PathVariable channel: String): ResponseEntity<BatchTrackingUpdateResponse> {
        log.info { "手动触发渠道轨迹更新 | 渠道: $channel" }
        
        try {
            val waybillChannel = WaybillChannel.valueOf(channel.uppercase())
            val result = trackingManagementService.updateTrackingByChannel(waybillChannel)
            return ResponseEntity.ok(BatchTrackingUpdateResponse.fromResult(result))
        } catch (e: IllegalArgumentException) {
            return ResponseEntity.badRequest().body(
                BatchTrackingUpdateResponse(0, 1, 0, listOf("不支持的渠道: $channel"))
            )
        }
    }

    /**
     * 手动触发轨迹更新定时任务
     */
    @PostMapping("/update/all")
    fun triggerTrackingUpdate(): ResponseEntity<String> {
        log.info { "手动触发轨迹更新定时任务" }

        try {
            // 在新线程中执行，避免阻塞请求
            Thread {
                trackingUpdateScheduler.updateTrackingInfo()
            }.start()

            return ResponseEntity.ok("轨迹更新任务已启动")
        } catch (e: Exception) {
            log.error(e) { "手动触发轨迹更新任务失败" }
            return ResponseEntity.internalServerError().body("启动失败: ${e.message}")
        }
    }

    /**
     * 根据运单ID刷新轨迹信息
     */
    @PostMapping("/refresh/waybill-id/{waybillId}")
    fun refreshTrackingByWaybillId(@PathVariable waybillId: Long): ResponseEntity<TrackingUpdateResponse> {
        log.info { "根据运单ID刷新轨迹信息 | 运单ID: $waybillId" }

        val result = trackingManagementService.refreshTrackingByWaybillId(waybillId)
        return ResponseEntity.ok(TrackingUpdateResponse.fromResult(result))
    }

    /**
     * 批量根据运单ID刷新轨迹信息
     */
    @PostMapping("/refresh/waybill-ids/batch")
    fun batchRefreshTrackingByWaybillIds(@RequestBody waybillIds: List<Long>): ResponseEntity<BatchTrackingUpdateResponse> {
        log.info { "批量根据运单ID刷新轨迹信息 | 运单ID数量: ${waybillIds.size}" }

        val result = trackingManagementService.batchRefreshTrackingByWaybillIds(waybillIds)
        return ResponseEntity.ok(BatchTrackingUpdateResponse.fromResult(result))
    }

    /**
     * 获取轨迹统计信息
     */
    @GetMapping("/statistics")
    fun getTrackingStatistics(
        @RequestParam(required = false) startDate: String?,
        @RequestParam(required = false) endDate: String?
    ): TrackingStatisticsResponse {
        val statistics = trackingManagementService.getTrackingStatistics(startDate, endDate)
        return TrackingStatisticsResponse.fromDomain(statistics)
    }

    /**
     * 清理过期轨迹信息
     */
    @PostMapping("/cleanup")
    fun cleanupStaleTracking(): ResponseEntity<CleanupResponse> {
        log.info { "手动触发轨迹清理" }
        
        val result = trackingManagementService.cleanupStaleTracking()
        return ResponseEntity.ok(CleanupResponse(result.deletedCount, "清理完成"))
    }

    /**
     * 获取支持的渠道列表
     */
    @GetMapping("/channels")
    fun getSupportedChannels(): List<ChannelInfo> {
        return WaybillChannel.entries.map { channel ->
            ChannelInfo(
                code = channel.name,
                name = channel.displayName,
                supported = channel in listOf(
                    WaybillChannel.YUNTU,
                    WaybillChannel.YW_HZ,
                    WaybillChannel.YW_QZ,
                    WaybillChannel.YW_GZ,
                    WaybillChannel.YW_YW
                )
            )
        }
    }
}

/**
 * 轨迹信息响应
 */
data class TrackingInfoResponse(
    val id: Long,
    val orderNos: List<String>,
    val waybillId: Long?,
    val waybillNo: String,
    val trackingNumber: String?,
    val channel: String,
    val currentStatus: String,
    val currentStatusDisplay: String,
    val destinationCountry: String?,
    val originCountry: String?,
    val lastMileProvider: LastMileProviderResponse?,
    val trackingEvents: List<TrackingEventResponse>,
    val deliveryDays: Int?,
    val podLinks: List<String>,
    val lastUpdatedAt: String,
    val lastEventTime: String?,
) {
    companion object {
        fun fromDomain(domain: TrackingInfo): TrackingInfoResponse {
            return TrackingInfoResponse(
                id = domain.id,
                orderNos = domain.orderNos,
                waybillId = domain.waybillId,
                waybillNo = domain.waybillNo,
                trackingNumber = domain.trackingNumber,
                channel = domain.channel,
                currentStatus = domain.currentStatus.code,
                currentStatusDisplay = domain.currentStatus.displayName,
                destinationCountry = domain.destinationCountry,
                originCountry = domain.originCountry,
                lastMileProvider = domain.lastMileProvider?.let { LastMileProviderResponse.fromDomain(it) },
                trackingEvents = domain.trackingEvents.map { TrackingEventResponse.fromDomain(it) },
                deliveryDays = domain.deliveryDays,
                podLinks = domain.podLinks,
                lastUpdatedAt = domain.lastUpdatedAt.toString(),
                lastEventTime = domain.lastEventTime?.toString(),
            )
        }
    }
}

/**
 * 末端服务商响应
 */
data class LastMileProviderResponse(
    val name: String,
    val telephone: String?,
    val website: String?
) {
    companion object {
        fun fromDomain(domain: io.github.clive.luxomssystem.domain.tracking.model.LastMileProvider): LastMileProviderResponse {
            return LastMileProviderResponse(
                name = domain.name,
                telephone = domain.telephone,
                website = domain.website
            )
        }
    }
}

/**
 * 轨迹事件响应
 */
data class TrackingEventResponse(
    val eventTime: String,
    val status: String,
    val statusDisplay: String,
    val description: String,
    val location: String?,
    val isLastMileEvent: Boolean
) {
    companion object {
        fun fromDomain(domain: io.github.clive.luxomssystem.domain.tracking.model.TrackingEvent): TrackingEventResponse {
            return TrackingEventResponse(
                eventTime = domain.eventTime.toString(),
                status = domain.status.code,
                statusDisplay = domain.status.displayName,
                description = domain.description,
                location = domain.getFullLocation(),
                isLastMileEvent = domain.isLastMileEvent
            )
        }
    }
}

/**
 * 轨迹更新响应
 */
data class TrackingUpdateResponse(
    val success: Boolean,
    val skipped: Boolean,
    val message: String,
    val trackingInfo: TrackingInfoResponse?
) {
    companion object {
        fun fromResult(result: io.github.clive.luxomssystem.application.tracking.TrackingUpdateResult): TrackingUpdateResponse {
            return TrackingUpdateResponse(
                success = result.success,
                skipped = result.skipped,
                message = result.message,
                trackingInfo = result.trackingInfo?.let { TrackingInfoResponse.fromDomain(it) }
            )
        }
    }
}

/**
 * 批量轨迹更新响应
 */
data class BatchTrackingUpdateResponse(
    val successCount: Int,
    val failureCount: Int,
    val skippedCount: Int,
    val errors: List<String>
) {
    companion object {
        fun fromResult(result: io.github.clive.luxomssystem.application.tracking.BatchTrackingUpdateResult): BatchTrackingUpdateResponse {
            return BatchTrackingUpdateResponse(
                successCount = result.successCount,
                failureCount = result.failureCount,
                skippedCount = result.skippedCount,
                errors = result.errors
            )
        }
    }
}

/**
 * 轨迹统计响应
 */
data class TrackingStatisticsResponse(
    val totalTracking: Long,
    val completedTracking: Long,
    val pendingTracking: Long,
    val statusCounts: Map<String, Long>,
    val channelCounts: Map<String, Long>
) {
    companion object {
        fun fromDomain(domain: io.github.clive.luxomssystem.application.tracking.TrackingStatistics): TrackingStatisticsResponse {
            return TrackingStatisticsResponse(
                totalTracking = domain.totalTracking,
                completedTracking = domain.completedTracking,
                pendingTracking = domain.pendingTracking,
                statusCounts = domain.statusCounts.mapKeys { it.key.displayName },
                channelCounts = domain.channelCounts
            )
        }
    }
}

/**
 * 清理响应
 */
data class CleanupResponse(
    val deletedCount: Int,
    val message: String
)

/**
 * 渠道信息
 */
data class ChannelInfo(
    val code: String,
    val name: String,
    val supported: Boolean
)
