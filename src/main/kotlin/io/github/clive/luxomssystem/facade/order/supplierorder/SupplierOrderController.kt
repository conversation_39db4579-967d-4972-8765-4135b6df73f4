package io.github.clive.luxomssystem.facade.order.supplierorder

import io.github.clive.luxomssystem.application.SupplierOrderApplicationService
import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.ext.removeCommonInvalidChars
import io.github.clive.luxomssystem.domain.UserExcelTask
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import io.github.clive.luxomssystem.facade.order.supplierorder.request.CreateSupplierOrderRequest
import io.github.clive.luxomssystem.facade.order.supplierorder.request.UpdateSupplierOrderRequest
import io.github.clive.luxomssystem.facade.order.supplierorder.response.SupplierOrderResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import jakarta.servlet.http.HttpServletResponse
import kotlinx.coroutines.runBlocking
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/supplier-orders")
class SupplierOrderController(
    private val supplierOrderApplicationService: SupplierOrderApplicationService,
) {
    data class SupplierOrderPageRequest(
        val orderNo: String?,
        val status: List<SupplierOrderStatus> = emptyList(),
        val fileName: String? = null,
        val spu: String? = null,
        val mainOrderId: Long? = null,
        val supplierId: Long? = null,
        var orderNos: String? = null,
    ) : PageReq()

    @PostMapping
    fun createSupplierOrder(
        @RequestBody request: CreateSupplierOrderRequest,
    ): SupplierOrderResponse = supplierOrderApplicationService.createSupplierOrder(request)

    @GetMapping("/{id}")
    fun getSupplierOrder(
        @PathVariable id: Long,
    ): SupplierOrderResponse = supplierOrderApplicationService.getSupplierOrderById(id)

    @PutMapping("/{id}")
    fun updateSupplierOrder(
        @PathVariable id: Long,
        @RequestBody request: UpdateSupplierOrderRequest,
    ): SupplierOrderResponse = supplierOrderApplicationService.updateSupplierOrder(id, request)

    @PatchMapping("/{id}/status")
    fun changeStatus(
        @PathVariable id: Long,
        @RequestParam newStatus: SupplierOrderStatus,
    ): SupplierOrderResponse = supplierOrderApplicationService.changeStatus(id, newStatus)

    @PostMapping("/page")
    fun page(
        req: SupplierOrderPageRequest,
        @RequestBody paramReq: SupplierOrderPageRequest,
    ) = supplierOrderApplicationService.pageQuery(
        req.apply {
            req.orderNos = paramReq.orderNos
        },
    )

    @GetMapping("/failed/page")
    fun failedPage(req: SupplierOrderPageRequest) = supplierOrderApplicationService.failedPage(req)

    @GetMapping("/list")
    fun listSupplierOrder(
        @RequestParam wayBillRelation: String,
    ): List<SupplierOrderResponse> = supplierOrderApplicationService.listSupplierOrder(wayBillRelation)

    @PostMapping("/{id}/exception:handle")
    fun handleException(
        @PathVariable id: Long,
    ) {
        supplierOrderApplicationService.handleException(id)
    }

    @PostMapping("/batch/exception:handle")
    fun handleException(
        @RequestBody ids: List<Long>,
    ) {
        supplierOrderApplicationService.handleException(ids)
    }

    @PostMapping("/{id}/cancel")
    fun cancel(
        @PathVariable id: Long,
    ) {
        supplierOrderApplicationService.cancel(id)
    }

    @PostMapping("/{id}/complete")
    fun complete(
        @PathVariable id: Long,
    ) {
        supplierOrderApplicationService.complete(id)
    }

    @PostMapping("/{id}/delivery")
    fun delivery(
        @PathVariable id: Long,
    ) {
        supplierOrderApplicationService.delivery(id)
    }

    @PostMapping("/export-excel")
    fun exportExcel(
        @RequestBody re: ExportExcelRequest,
    ) {
        val id = supplierOrderApplicationService.save(re.fileName.removeCommonInvalidChars()!!, re.ids)
        val bizId = UserContextHolder.user!!.bizId
        Thread {
            runBlocking {
                supplierOrderApplicationService.exportContent(
                    re.fileName.removeCommonInvalidChars()!!,
                    id,
                    re.ids,
                    bizId,
                )
            }
        }.start()
    }

    @PostMapping("/export-label")
    fun exportLabel(
        @RequestBody re: ExportExcelRequest,
        response: HttpServletResponse,
    ) {
        supplierOrderApplicationService.exportLabel(re.fileName, re.ids, response)
    }

    @GetMapping("/export-excel/page")
    fun pageQuery(req: PageReq): PageResponse<UserExcelTask> = supplierOrderApplicationService.pageQueryExcelTask(req)

    @GetMapping("/list/by-order")
    fun listSupplierOrderByMainOrderId(mainOrderId: Long): List<SupplierOrderResponse> =
        supplierOrderApplicationService.listSupplierOrderByMainOrderId(mainOrderId)

    data class ExportExcelRequest(
        val fileName: String,
        val ids: List<Long>,
    )
}
