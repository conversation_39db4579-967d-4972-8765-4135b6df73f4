package io.github.clive.luxomssystem.facade.order.waybill.request

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus

data class WaybillPageRequest(
    val orderNo: String? = null,
    val status: List<WayBillStatus> = emptyList(),
    val spu: String? = null,
    val fileName: String? = null,
    var mainOrderId: Long? = null,
    var waybillNo: String? = null,
    var orderNos: String? = null,
    var waybillNos: String? = null,
    val country: String? = null,
) : PageReq()
