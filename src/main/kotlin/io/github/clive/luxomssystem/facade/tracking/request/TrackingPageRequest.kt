package io.github.clive.luxomssystem.facade.tracking.request

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.domain.tracking.model.TrackingStatus

/**
 * 轨迹分页查询请求
 */
data class TrackingPageRequest(
    /**
     * 运单号
     */
    val waybillNo: String? = null,

    /**
     * 跟踪号
     */
    val trackingNumber: String? = null,

    /**
     * 订单号
     */
    val orderNo: String? = null,

    /**
     * 渠道
     */
    val channel: String? = null,

    /**
     * 状态
     */
    val status: List<TrackingStatus> = emptyList(),

    /**
     * 是否完成
     */
    val completed: Boolean? = null,

    /**
     * 目的地国家
     */
    val destinationCountry: String? = null,

    /**
     * 目的地国家代码
     */
    val destinationCountryCode: String? = null,

    /**
     * 起始国家
     */
    val originCountry: String? = null,

    /**
     * 起始国家代码
     */
    val originCountryCode: String? = null,

    /**
     * 运单ID
     */
    val waybillId: Long? = null,

    /**
     * 批量订单号（多个订单号用换行分隔）
     */
    var orderNos: String? = null,

    /**
     * 批量运单号（多个运单号用换行分隔）
     */
    var waybillNos: String? = null,

    /**
     * 批量跟踪号（多个跟踪号用换行分隔）
     */
    var trackingNumbers: String? = null
) : PageReq()
