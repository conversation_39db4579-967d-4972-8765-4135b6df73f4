package io.github.clive.luxomssystem.infrastructure.tracking.repository

import io.github.clive.luxomssystem.domain.tracking.model.TrackingInfo
import io.github.clive.luxomssystem.domain.tracking.model.TrackingStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.ZonedDateTime

/**
 * 轨迹信息Repository
 */
@Repository
interface TrackingInfoRepository : JpaRepository<TrackingInfo, Long> {

    /**
     * 根据运单号查找轨迹信息
     */
    fun findByWaybillNo(waybillNo: String): TrackingInfo?

    /**
     * 根据订单号查找轨迹信息
     */
    @Query("select * from tracking_info where :orderNo = any(order_nos)", nativeQuery = true)
    fun findByOrderNos(@Param("orderNo") orderNo: String): List<TrackingInfo>

    /**
     * 根据运单ID查找轨迹信息
     */
    fun findByWaybillId(waybillId: Long): TrackingInfo?

    /**
     * 根据主订单ID查找轨迹信息
     */
    fun findByMainOrderId(mainOrderId: Long): List<TrackingInfo>

    /**
     * 查找完成的轨迹信息
     */
    @Query("""
        SELECT t FROM TrackingInfo t 
        WHERE  t.currentStatus = :status
    """)
    fun findByIsCompleted(@Param("status") status: TrackingStatus): List<TrackingInfo>

    /**
     * 查找超过指定天数未更新的轨迹信息
     */
    @Query("""
        SELECT t FROM TrackingInfo t 
        WHERE t.lastUpdatedAt < :cutoffTime
    """)
    fun findTrackingInfoOlderThan(@Param("cutoffTime") cutoffTime: ZonedDateTime): List<TrackingInfo>

    /**
     * 查找指定渠道且未完成的轨迹信息
     */
    fun findByChannel(channel: String): List<TrackingInfo>

    /**
     * 统计各状态的轨迹数量
     */
    @Query("""
        SELECT t.currentStatus, COUNT(t) 
        FROM TrackingInfo t 
        GROUP BY t.currentStatus
    """)
    fun countByStatus(): List<Array<Any>>

    /**
     * 统计各渠道的轨迹数量
     */
    @Query("""
        SELECT t.channel, COUNT(t)
        FROM TrackingInfo t
        GROUP BY t.channel
    """)
    fun countByChannel(): List<Array<Any>>

    /**
     * 按时间范围统计各状态的轨迹数量
     */
    @Query("""
        SELECT t.currentStatus, COUNT(t)
        FROM TrackingInfo t
        WHERE t.createdAt >= :startDate AND t.createdAt <= :endDate
        GROUP BY t.currentStatus
    """)
    fun countByStatusWithDateRange(
        @Param("startDate") startDate: Long,
        @Param("endDate") endDate: Long
    ): List<Array<Any>>

    /**
     * 按时间范围统计各渠道的轨迹数量
     */
    @Query("""
        SELECT t.channel, COUNT(t)
        FROM TrackingInfo t
        WHERE t.createdAt >= :startDate AND t.createdAt <= :endDate
        GROUP BY t.channel
    """)
    fun countByChannelWithDateRange(
        @Param("startDate") startDate: Long,
        @Param("endDate") endDate: Long
    ): List<Array<Any>>

    /**
     * 按时间范围统计总轨迹数量
     */
    @Query("""
        SELECT COUNT(t)
        FROM TrackingInfo t
        WHERE t.createdAt >= :startDate AND t.createdAt <= :endDate
    """)
    fun countByDateRange(
        @Param("startDate") startDate: Long,
        @Param("endDate") endDate: Long
    ): Long

    /**
     * 按状态和时间范围统计轨迹数量
     */
    @Query("""
        SELECT COUNT(t)
        FROM TrackingInfo t
        WHERE t.currentStatus = :status
        AND t.createdAt >= :startDate AND t.createdAt <= :endDate
    """)
    fun countByStatusAndDateRange(
        @Param("status") status: TrackingStatus,
        @Param("startDate") startDate: Long,
        @Param("endDate") endDate: Long
    ): Long

    /**
     * 按时间范围统计过期轨迹数量
     */
    @Query("""
        SELECT COUNT(t)
        FROM TrackingInfo t
        WHERE t.lastUpdatedAt < :cutoffTime
        AND t.createdAt >= :startDate AND t.createdAt <= :endDate
    """)
    fun countStaleTrackingWithDateRange(
        @Param("cutoffTime") cutoffTime: ZonedDateTime,
        @Param("startDate") startDate: Long,
        @Param("endDate") endDate: Long
    ): Long

    /**
     * 流式查询需要更新的轨迹信息（分页处理）
     * 条件：未完成且最后更新时间超过指定时间且最后事件时间未超过15天
     */
    @Query("""
        SELECT t FROM TrackingInfo t
        WHERE t.lastUpdatedAt < :updateCutoffTime
        AND (t.lastEventTime IS NULL OR t.lastEventTime >= :staleCutoffTime)
        ORDER BY t.lastUpdatedAt ASC
    """)
    fun findTrackingInfoForUpdate(
        @Param("updateCutoffTime") updateCutoffTime: ZonedDateTime,
        @Param("staleCutoffTime") staleCutoffTime: ZonedDateTime,
        pageable: org.springframework.data.domain.Pageable
    ): org.springframework.data.domain.Page<TrackingInfo>

    /**
     * 统计需要更新的轨迹数量
     */
    @Query("""
        SELECT COUNT(t) FROM TrackingInfo t
        WHERE t.lastUpdatedAt < :updateCutoffTime
        AND (t.lastEventTime IS NULL OR t.lastEventTime >= :staleCutoffTime)
    """)
    fun countTrackingInfoForUpdate(
        @Param("updateCutoffTime") updateCutoffTime: ZonedDateTime,
        @Param("staleCutoffTime") staleCutoffTime: ZonedDateTime
    ): Long

    /**
     * 分页查询轨迹信息
     */
    @Query("""
        SELECT * FROM tracking_info t
        WHERE (:waybillNo IS NULL OR t.waybill_no  = :waybillNo)
        AND (:trackingNumber IS NULL OR t.tracking_number  = :trackingNumber)
        AND (:orderNo IS NULL OR :orderNo = ANY(t.order_nos))
        AND (:channel IS NULL OR t.channel = :channel)
        AND (:destinationCountry IS NULL OR (UPPER(t.destination_country) = UPPER(:destinationCountry) OR LENGTH(:destinationCountry) = 2 AND UPPER(t.destination_country) LIKE UPPER(:destinationCountry) || '%'))
        AND (:originCountry IS NULL OR (UPPER(t.origin_country) = UPPER(:originCountry) OR LENGTH(:originCountry) = 2 AND UPPER(t.origin_country) LIKE UPPER(:originCountry) || '%'))
        AND (:waybillId IS NULL OR t.waybill_id = :waybillId)
        AND (:createdAtFrom IS NULL OR t.created_at >= :createdAtFrom)
        AND (:createdAtTo IS NULL OR t.created_at <= :createdAtTo)
        AND (:#{#currentStatus.isEmpty()} = true OR t.current_status IN :currentStatus)
        AND (:#{#orderNos.isEmpty()} = true OR EXISTS (SELECT 1 FROM unnest(t.order_nos) AS order_no WHERE order_no IN :orderNos))
        AND (:#{#waybillNos.isEmpty()} = true OR t.waybill_no IN :waybillNos)
        AND (:#{#trackingNumbers.isEmpty()} = true OR t.tracking_number IN :trackingNumbers)
        ORDER BY t.last_updated_at DESC
    """, nativeQuery = true)
    fun pageQuery(
        @Param("waybillNo") waybillNo: String?,
        @Param("trackingNumber") trackingNumber: String?,
        @Param("orderNo") orderNo: String?,
        @Param("channel") channel: String?,
        @Param("destinationCountry") destinationCountry: String?,
        @Param("originCountry") originCountry: String?,
        @Param("waybillId") waybillId: Long?,
        @Param("createdAtFrom") createdAtFrom: Long?,
        @Param("createdAtTo") createdAtTo: Long?,
        @Param("currentStatus") currentStatus: List<String>,
        pageable: Pageable,
        @Param("orderNos") orderNos: List<String> = emptyList(),
        @Param("waybillNos") waybillNos: List<String> = emptyList(),
        @Param("trackingNumbers") trackingNumbers: List<String> = emptyList()
    ): Page<TrackingInfo>

}
