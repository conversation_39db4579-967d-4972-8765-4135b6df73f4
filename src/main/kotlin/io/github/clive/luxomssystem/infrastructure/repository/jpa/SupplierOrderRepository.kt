package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrder
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderStatus
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SupplierOrderRepository : JpaRepository<SupplierOrder, Long> {
    fun findByOrderNo(orderNo: String): SupplierOrder?

    fun findByBizIdAndShipping_WayBillRelation(
        bizId: Long,
        wayBillRelation: String,
    ): List<SupplierOrder>

    fun findByBizIdAndOrderNoIn(
        bizId: Long,
        orderNos: List<String>,
    ): List<SupplierOrder>

    fun findByOrderNoAndBizId(
        orderNo: String,
        bizId: Long,
    ): SupplierOrder?

    fun findByOrderNoIn(orderNos: List<String>): List<SupplierOrder>

    @Query(
        """
        select so from supplier_orders so
        WHERE :orderNo ILIKE so.orderNo || '%' and so.bizId = :bizId
        """,
    )
    fun queryByOrderNoAndBizId(
        orderNo: String,
        bizId: Long,
    ): List<SupplierOrder>

    @Query(
        """
        select so from supplier_orders so
        where
            so.bizId = :bizId
            and 1=1
            and so.accepted = true
            and (:orderNo is null or so.orderNo ilike %:orderNo% )
            and (:supplierId is null or so.product.supplierId = :supplierId)
            and (:#{#statues.isEmpty()} = true or so.status IN :statues)
            and (:spu is null or so.product.spu ilike %:spu%)
            and (:mainOrderId is null or so.mainOrderId = :mainOrderId)
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
            and (:fileName is null or so.fileName ilike %:fileName%)
            and (:#{#orderNos.isEmpty()} = true or so.orderNo IN :orderNos)
            and (:country is null or so.recipient.country ilike %:country%)

        order by so.createdAt desc
    """,
    )
    fun page(
        supplierId: Long?,
        bizId: Long,
        orderNo: String?,
        statues: List<SupplierOrderStatus>,
        pageable: Pageable,
        spu: String?,
        mainOrderId: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        fileName: String?,
        orderNos: List<String>? = null,
        country: String? = null,
    ): Page<SupplierOrder>

    @Query(
        """
        select so from supplier_orders so
        where
            so.bizId = :bizId
            and (:orderNo is null or so.orderNo ilike %:orderNo% )
            and (:#{#statues.isEmpty()} = true or so.status IN :statues)
            and (:spu is null or so.product.spu ilike %:spu%)
            and (:mainOrderId is null or so.mainOrderId = :mainOrderId)
            and (:createdAtFrom is null or so.createdAt >= :createdAtFrom)
            and (:createdAtTo is null or so.createdAt <= :createdAtTo)
            and (:fileName is null or so.fileName ilike %:fileName%)
            and (:supplierId is null or so.product.supplierId = :supplierId)
            and (:#{#orderNos.isEmpty()} = true or so.orderNo IN :orderNos)
            and (:country is null or so.recipient.country ilike %:country%)
        order by so.createdAt desc
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun pageNoSupplier(
        bizId: Long,
        orderNo: String?,
        statues: List<SupplierOrderStatus>,
        pageable: Pageable,
        spu: String?,
        mainOrderId: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        fileName: String?,
        supplierId: Long?,
        orderNos: List<String>? = null,
        country: String? = null,
    ): Page<SupplierOrder>

    fun findByMainOrderId(mainOrderId: Long): List<SupplierOrder>

    fun findByMainOrderIdAndProduct_SupplierId(
        mainOrderId: Long,
        supplierId: Long,
    ): List<SupplierOrder>

    @Query(
        """
                select count(so) from supplier_orders so where
                1=1 and
                (:failed is null or so.status = :failed)
                and so.createdAt >= :createdAtFromEpochMilli
                and so.createdAt <= :createdAtToEpochMilli
                and so.bizId = :bizId
        """,
    )
    fun countByStatusAndCreatedAtBetween(
        failed: SupplierOrderStatus?,
        createdAtFromEpochMilli: Long?,
        createdAtToEpochMilli: Long?,
        bizId: Long,
    ): Int

    @Query(
        """
        select distinct so.product.supplierId from supplier_orders so
        where so.mainOrderId = :mainOrderId
        """,
    )
    fun findMainOrderAssignedSuppliers(mainOrderId: Long): List<Long?>

    fun deleteByMainOrderId(mainOrderId: Long)

    fun existsByWaybillIdAndStatusNot(
        waybillId: Long,
        status: SupplierOrderStatus): Boolean
}
